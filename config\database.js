/**
 * إعدادات قاعدة البيانات JSON
 * JSON Database Configuration
 */

const path = require('path');

const databaseConfig = {
    // مسار مجلد البيانات
    dataPath: path.join(__dirname, '../data'),
    
    // أسماء ملفات البيانات
    files: {
        accounts: 'accounts.json',
        montage: 'montage.json',
        inventory: 'inventory.json',
        loginLogs: 'login-logs.json',
        invoices: 'invoices.json',
        updates: 'updates.json'
    },
    
    // إعدادات النسخ الاحتياطية
    backup: {
        enabled: true,
        path: path.join(__dirname, '../data/backups'),
        maxBackups: 30, // الحد الأقصى لعدد النسخ الاحتياطية
        autoBackup: true, // إنشاء نسخة احتياطية تلقائياً قبل كل تعديل
        backupInterval: 24 * 60 * 60 * 1000 // 24 ساعة بالميلي ثانية
    },
    
    // إعدادات الأمان
    security: {
        encryptPasswords: true,
        logAllOperations: true,
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000 // 15 دقيقة
    },
    
    // إعدادات التحقق من صحة البيانات
    validation: {
        validateOnRead: true,
        validateOnWrite: true,
        strictMode: false
    },
    
    // إعدادات الأداء
    performance: {
        cacheEnabled: true,
        cacheTimeout: 5 * 60 * 1000, // 5 دقائق
        maxFileSize: 10 * 1024 * 1024 // 10 ميجابايت
    }
};

module.exports = databaseConfig;

/**
 * دوال مساعدة لإدارة البيانات
 * Data Management Helper Functions
 */

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * التحقق من صحة رقم الهاتف المصري
 */
function validateEgyptianPhone(phone) {
    const phoneRegex = /^(010|011|012|015)[0-9]{8}$/;
    return phoneRegex.test(phone.replace(/[\s-]/g, ''));
}

/**
 * تنسيق رقم الهاتف
 */
function formatPhone(phone) {
    const cleaned = phone.replace(/[\s-]/g, '');
    if (cleaned.length === 11) {
        return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
}

/**
 * تنسيق التاريخ للعرض
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

/**
 * تنسيق الوقت للعرض
 */
function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString('ar-EG', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * تنسيق المبلغ المالي
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP'
    }).format(amount);
}

/**
 * إنشاء معرف فريد
 */
function generateUniqueId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * تنظيف النص من المسافات الزائدة
 */
function cleanText(text) {
    return text ? text.trim().replace(/\s+/g, ' ') : '';
}

/**
 * التحقق من صحة كلمة المرور
 */
function validatePassword(password) {
    // كلمة المرور يجب أن تكون 8 أحرف على الأقل
    if (password.length < 8) {
        return { valid: false, message: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' };
    }
    
    // يجب أن تحتوي على حرف كبير وصغير ورقم
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    
    if (!hasUpper || !hasLower || !hasNumber) {
        return { 
            valid: false, 
            message: 'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم' 
        };
    }
    
    return { valid: true, message: 'كلمة مرور صحيحة' };
}

/**
 * حساب النسبة المئوية
 */
function calculatePercentage(value, total) {
    if (total === 0) return 0;
    return Math.round((value / total) * 100 * 100) / 100;
}

/**
 * حساب الضريبة
 */
function calculateTax(amount, taxRate = 0.14) {
    return Math.round(amount * taxRate * 100) / 100;
}

/**
 * حساب الخصم
 */
function calculateDiscount(amount, discountRate) {
    if (discountRate > 1) {
        // إذا كان الخصم أكبر من 1، فهو مبلغ ثابت
        return discountRate;
    } else {
        // إذا كان أقل من أو يساوي 1، فهو نسبة مئوية
        return Math.round(amount * discountRate * 100) / 100;
    }
}

/**
 * البحث في النص العربي
 */
function searchArabicText(text, searchTerm) {
    if (!text || !searchTerm) return false;
    
    const normalizedText = text.toLowerCase().trim();
    const normalizedSearch = searchTerm.toLowerCase().trim();
    
    return normalizedText.includes(normalizedSearch);
}

/**
 * ترقيم الصفحات
 */
function paginate(array, page = 1, limit = 10) {
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    
    return {
        data: array.slice(startIndex, endIndex),
        currentPage: page,
        totalPages: Math.ceil(array.length / limit),
        totalItems: array.length,
        hasNext: endIndex < array.length,
        hasPrev: page > 1
    };
}

/**
 * ترتيب المصفوفة حسب خاصية معينة
 */
function sortByProperty(array, property, direction = 'asc') {
    return array.sort((a, b) => {
        let aVal = a[property];
        let bVal = b[property];
        
        // التعامل مع التواريخ
        if (typeof aVal === 'string' && aVal.includes('T')) {
            aVal = new Date(aVal);
            bVal = new Date(bVal);
        }
        
        if (direction === 'asc') {
            return aVal > bVal ? 1 : -1;
        } else {
            return aVal < bVal ? 1 : -1;
        }
    });
}

/**
 * تصفية المصفوفة حسب معايير متعددة
 */
function filterByMultipleCriteria(array, filters) {
    return array.filter(item => {
        return Object.keys(filters).every(key => {
            const filterValue = filters[key];
            const itemValue = item[key];
            
            if (filterValue === null || filterValue === undefined || filterValue === '') {
                return true;
            }
            
            if (typeof filterValue === 'string') {
                return searchArabicText(itemValue, filterValue);
            }
            
            return itemValue === filterValue;
        });
    });
}

module.exports = {
    validateEmail,
    validateEgyptianPhone,
    formatPhone,
    formatDate,
    formatTime,
    formatCurrency,
    generateUniqueId,
    cleanText,
    validatePassword,
    calculatePercentage,
    calculateTax,
    calculateDiscount,
    searchArabicText,
    paginate,
    sortByProperty,
    filterByMultipleCriteria
};

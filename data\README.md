# نظام البيانات JSON - JSON Data System

هذا المجلد يحتوي على جميع ملفات البيانات للنظام بصيغة JSON.

## ملفات البيانات:

### 1. accounts.json - الحسابات
يحتوي على:
- بيانات المستخدمين والموظفين
- الأدوار والصلاحيات
- معلومات تسجيل الدخول

### 2. montage.json - المونتاج
يحتوي على:
- طلبات المونتاج
- بيانات الفنيين
- مواعيد التركيب
- حالة طلبات المونتاج

### 3. inventory.json - المخزن
يحتوي على:
- المنتجات والمفروشات
- الكميات المتاحة
- الفئات والأصناف
- بيانات الموردين
- أسعار التكلفة والبيع

### 4. login-logs.json - سجلات الدخول
يحتوي على:
- سجلات تسجيل الدخول والخروج
- محاولات الدخول الفاشلة
- الجلسات النشطة
- عناوين IP ومعلومات المتصفح

### 5. invoices.json - الفواتير
يحتوي على:
- الفواتير والمبيعات
- بيانات العملاء
- تفاصيل المنتجات المباعة
- طرق الدفع
- حالة التسليم والمونتاج

### 6. updates.json - التحديثات
يحتوي على:
- تحديثات النظام
- تحديثات البيانات
- تحديثات الأسعار
- تحديثات المخزون
- سجل التغييرات

## النسخ الاحتياطية:
- يتم إنشاء نسخ احتياطية تلقائياً في مجلد `backups/`
- النسخ الاحتياطية تحمل تاريخ ووقت الإنشاء
- يُنصح بالاحتفاظ بالنسخ الاحتياطية لمدة شهر على الأقل

## الأمان:
- جميع كلمات المرور محفوظة بشكل مشفر
- يتم تسجيل جميع العمليات في ملف التحديثات
- النسخ الاحتياطية تُنشأ قبل أي تعديل مهم

## ملاحظات:
- لا تقم بتعديل الملفات يدوياً إلا في حالات الطوارئ
- استخدم واجهة النظام لجميع العمليات
- تأكد من صحة تنسيق JSON قبل أي تعديل يدوي

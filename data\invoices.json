{"invoices": [{"id": 1, "invoiceNumber": "INV-2024-001", "customerId": 1, "customerInfo": {"name": "<PERSON>حمد محمد علي", "phone": "01234567890", "email": "<EMAIL>", "address": "القاهرة، مصر الجديدة، شارع الحجاز"}, "items": [{"productId": 1, "productName": "غرفة نوم كاملة - موديل كلاسيك", "quantity": 1, "unitPrice": 15000, "discount": 500, "totalPrice": 14500}], "subtotal": 14500, "taxRate": 0.14, "taxAmount": 2030, "discountAmount": 500, "shippingCost": 200, "totalAmount": 16730, "paymentMethod": "cash", "paymentStatus": "paid", "paidAmount": 16730, "remainingAmount": 0, "deliveryDate": "2024-01-15", "deliveryAddress": "القاهرة، مصر الجديدة، شارع الحجاز", "deliveryNotes": "التسليم في المساء", "montageRequired": true, "montageDate": "2024-01-16", "status": "completed", "notes": "عميل مميز", "salesPersonId": 1, "salesPersonName": "<PERSON><PERSON><PERSON><PERSON> أحمد", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}], "customers": [{"id": 1, "name": "<PERSON>حمد محمد علي", "phone": "01234567890", "email": "<EMAIL>", "address": "القاهرة، مصر الجديدة، شارع الحجاز", "city": "القاهرة", "governorate": "القاهرة", "customerType": "individual", "totalPurchases": 16730, "lastPurchaseDate": "2024-01-01", "notes": "عميل مميز", "isActive": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}], "paymentMethods": [{"id": "cash", "name": "نقدي", "isActive": true}, {"id": "card", "name": "بطاقة ائتمان", "isActive": true}, {"id": "installment", "name": "تقسيط", "isActive": true}, {"id": "bank_transfer", "name": "تحويل بنكي", "isActive": true}], "nextInvoiceId": 2, "nextCustomerId": 2, "invoiceCounter": 1}
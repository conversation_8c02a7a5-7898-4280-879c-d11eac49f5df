/**
 * ثوابت التطبيق
 * Application Constants
 */

// أدوار المستخدمين
const USER_ROLES = {
    ADMIN: 'admin',
    MANAGER: 'manager',
    SALES: 'sales',
    INVENTORY: 'inventory',
    TECHNICIAN: 'technician'
};

// حالات الطلبات
const ORDER_STATUS = {
    PENDING: 'pending',
    CONFIRMED: 'confirmed',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled'
};

// حالات المونتاج
const MONTAGE_STATUS = {
    PENDING: 'pending',
    SCHEDULED: 'scheduled',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled'
};

// حالات الدفع
const PAYMENT_STATUS = {
    PENDING: 'pending',
    PARTIAL: 'partial',
    PAID: 'paid',
    REFUNDED: 'refunded'
};

// طرق الدفع
const PAYMENT_METHODS = {
    CASH: 'cash',
    CARD: 'card',
    INSTALLMENT: 'installment',
    BANK_TRANSFER: 'bank_transfer'
};

// فئات المنتجات
const PRODUCT_CATEGORIES = {
    BEDROOM: 'غرف النوم',
    LIVING_ROOM: 'غرف المعيشة',
    DINING_ROOM: 'غرف الطعام',
    OFFICE: 'المكاتب',
    KITCHEN: 'المطابخ',
    CHILDREN: 'غرف الأطفال'
};

// أنواع التحديثات
const UPDATE_TYPES = {
    SYSTEM: 'system',
    DATA: 'data',
    PRICE: 'price',
    INVENTORY: 'inventory'
};

// مستويات الأولوية
const PRIORITY_LEVELS = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    URGENT: 'urgent'
};

// حالات المخزون
const INVENTORY_STATUS = {
    IN_STOCK: 'in_stock',
    LOW_STOCK: 'low_stock',
    OUT_OF_STOCK: 'out_of_stock',
    DISCONTINUED: 'discontinued'
};

// أنواع العملاء
const CUSTOMER_TYPES = {
    INDIVIDUAL: 'individual',
    CORPORATE: 'corporate',
    WHOLESALE: 'wholesale'
};

// الرسائل
const MESSAGES = {
    SUCCESS: {
        CREATED: 'تم الإنشاء بنجاح',
        UPDATED: 'تم التحديث بنجاح',
        DELETED: 'تم الحذف بنجاح',
        SAVED: 'تم الحفظ بنجاح'
    },
    ERROR: {
        NOT_FOUND: 'العنصر غير موجود',
        INVALID_DATA: 'البيانات غير صحيحة',
        UNAUTHORIZED: 'غير مصرح لك بهذا الإجراء',
        SERVER_ERROR: 'خطأ في الخادم'
    },
    VALIDATION: {
        REQUIRED: 'هذا الحقل مطلوب',
        INVALID_EMAIL: 'البريد الإلكتروني غير صحيح',
        INVALID_PHONE: 'رقم الهاتف غير صحيح',
        PASSWORD_TOO_SHORT: 'كلمة المرور قصيرة جداً'
    }
};

// إعدادات النظام
const SYSTEM_SETTINGS = {
    DEFAULT_TAX_RATE: 0.14,
    DEFAULT_CURRENCY: 'EGP',
    DEFAULT_LANGUAGE: 'ar',
    MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif'],
    SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes
    MAX_LOGIN_ATTEMPTS: 5
};

// أنماط التاريخ والوقت
const DATE_FORMATS = {
    DISPLAY: 'DD/MM/YYYY',
    DATABASE: 'YYYY-MM-DD',
    DATETIME: 'DD/MM/YYYY HH:mm',
    TIME: 'HH:mm'
};

module.exports = {
    USER_ROLES,
    ORDER_STATUS,
    MONTAGE_STATUS,
    PAYMENT_STATUS,
    PAYMENT_METHODS,
    PRODUCT_CATEGORIES,
    UPDATE_TYPES,
    PRIORITY_LEVELS,
    INVENTORY_STATUS,
    CUSTOMER_TYPES,
    MESSAGES,
    SYSTEM_SETTINGS,
    DATE_FORMATS
};

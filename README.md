# Furniture Store Sales Management System

نظام إدارة مبيعات معرض المفروشات

## الوصف
تطبيق ويب لإدارة مبيعات معرض المفروشات باستخدام Node.js و Express.js

## الميزات المخططة
- إدارة المنتجات (المفروشات)
- إدارة العملاء
- إدارة المبيعات
- تقارير المبيعات
- نظام المصادقة والتفويض
- إدارة المخزون

## التقنيات المستخدمة
- Node.js
- Express.js
- MySQL/MongoDB (سيتم تحديدها لاحقاً)
- Handlebars (قوالب العرض)
- Bootstrap (التصميم)

## هيكل المشروع
```
├── src/                    # الكود المصدري الرئيسي
├── config/                 # ملفات التكوين
├── database/               # قاعدة البيانات والمخططات
├── public/                 # الملفات العامة
├── views/                  # قوالب العرض
├── routes/                 # مسارات التطبيق
├── middleware/             # الوسطاء
├── models/                 # نماذج البيانات
├── controllers/            # المتحكمات
├── services/               # الخدمات
├── utils/                  # الأدوات المساعدة
├── tests/                  # الاختبارات
└── docs/                   # التوثيق
```

## التثبيت والتشغيل
سيتم إضافة تعليمات التثبيت والتشغيل لاحقاً.

## المساهمة
سيتم إضافة إرشادات المساهمة لاحقاً.

## الترخيص
ISC

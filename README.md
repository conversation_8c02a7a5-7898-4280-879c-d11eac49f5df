# Furniture Store Sales Management System

نظام إدارة مبيعات معرض المفروشات

## الوصف
تطبيق ويب لإدارة مبيعات معرض المفروشات باستخدام Node.js و Express.js

## الميزات المخططة
- إدارة المنتجات (المفروشات)
- إدارة العملاء
- إدارة المبيعات
- تقارير المبيعات
- نظام المصادقة والتفويض
- إدارة المخزون

## التقنيات المستخدمة
- Node.js
- Express.js
- JSON Files (نظام حفظ البيانات)
- Handlebars (قوالب العرض)
- Bootstrap (التصميم)

## هيكل المشروع
```
├── src/                    # الكود المصدري الرئيسي
├── config/                 # ملفات التكوين
│   └── database.js         # إعدادات قاعدة البيانات JSON
├── data/                   # ملفات البيانات JSON
│   ├── accounts.json       # الحسابات والمستخدمين
│   ├── montage.json        # طلبات المونتاج والفنيين
│   ├── inventory.json      # المخزون والمنتجات
│   ├── login-logs.json     # سجلات تسجيل الدخول
│   ├── invoices.json       # الفواتير والعملاء
│   ├── updates.json        # سجل التحديثات
│   └── backups/            # النسخ الاحتياطية
├── public/                 # الملفات العامة
├── views/                  # قوالب العرض
├── routes/                 # مسارات التطبيق
├── middleware/             # الوسطاء
├── models/                 # نماذج البيانات
├── controllers/            # المتحكمات
├── services/               # الخدمات
│   └── DatabaseService.js  # خدمة إدارة البيانات JSON
├── utils/                  # الأدوات المساعدة
│   ├── dataHelpers.js      # دوال مساعدة للبيانات
│   └── constants.js        # ثوابت التطبيق
├── tests/                  # الاختبارات
└── docs/                   # التوثيق
```

## التثبيت والتشغيل
سيتم إضافة تعليمات التثبيت والتشغيل لاحقاً.

## المساهمة
سيتم إضافة إرشادات المساهمة لاحقاً.

## الترخيص
ISC

const fs = require('fs').promises;
const path = require('path');

/**
 * خدمة إدارة قاعدة البيانات JSON
 * JSON Database Service
 */
class DatabaseService {
    constructor() {
        this.dataPath = path.join(__dirname, '../data');
        this.files = {
            accounts: 'accounts.json',
            montage: 'montage.json',
            inventory: 'inventory.json',
            loginLogs: 'login-logs.json',
            invoices: 'invoices.json',
            updates: 'updates.json'
        };
    }

    /**
     * قراءة ملف JSON
     * Read JSON file
     */
    async readFile(fileName) {
        try {
            const filePath = path.join(this.dataPath, fileName);
            const data = await fs.readFile(filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error(`Error reading file ${fileName}:`, error);
            throw new Error(`Failed to read ${fileName}`);
        }
    }

    /**
     * كتابة ملف JSON
     * Write JSON file
     */
    async writeFile(fileName, data) {
        try {
            const filePath = path.join(this.dataPath, fileName);
            await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
            return true;
        } catch (error) {
            console.error(`Error writing file ${fileName}:`, error);
            throw new Error(`Failed to write ${fileName}`);
        }
    }

    /**
     * إنشاء نسخة احتياطية من ملف
     * Create backup of file
     */
    async createBackup(fileName) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupFileName = `${fileName.replace('.json', '')}_backup_${timestamp}.json`;
            const originalPath = path.join(this.dataPath, fileName);
            const backupPath = path.join(this.dataPath, 'backups', backupFileName);
            
            // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            await fs.mkdir(path.join(this.dataPath, 'backups'), { recursive: true });
            
            const data = await fs.readFile(originalPath, 'utf8');
            await fs.writeFile(backupPath, data, 'utf8');
            
            return backupFileName;
        } catch (error) {
            console.error(`Error creating backup for ${fileName}:`, error);
            throw new Error(`Failed to create backup for ${fileName}`);
        }
    }

    // ==================== الحسابات - Accounts ====================

    /**
     * الحصول على جميع الحسابات
     */
    async getAccounts() {
        return await this.readFile(this.files.accounts);
    }

    /**
     * إضافة حساب جديد
     */
    async addAccount(accountData) {
        const data = await this.getAccounts();
        const newAccount = {
            id: data.nextId,
            ...accountData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        data.accounts.push(newAccount);
        data.nextId++;
        
        await this.writeFile(this.files.accounts, data);
        return newAccount;
    }

    /**
     * تحديث حساب
     */
    async updateAccount(accountId, updateData) {
        const data = await this.getAccounts();
        const accountIndex = data.accounts.findIndex(acc => acc.id === accountId);
        
        if (accountIndex === -1) {
            throw new Error('Account not found');
        }
        
        data.accounts[accountIndex] = {
            ...data.accounts[accountIndex],
            ...updateData,
            updatedAt: new Date().toISOString()
        };
        
        await this.writeFile(this.files.accounts, data);
        return data.accounts[accountIndex];
    }

    // ==================== المونتاج - Montage ====================

    /**
     * الحصول على بيانات المونتاج
     */
    async getMontageData() {
        return await this.readFile(this.files.montage);
    }

    /**
     * إضافة طلب مونتاج جديد
     */
    async addMontageOrder(orderData) {
        const data = await this.getMontageData();
        const newOrder = {
            id: data.nextOrderId,
            ...orderData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        data.montageOrders.push(newOrder);
        data.nextOrderId++;
        
        await this.writeFile(this.files.montage, data);
        return newOrder;
    }

    // ==================== المخزن - Inventory ====================

    /**
     * الحصول على بيانات المخزن
     */
    async getInventoryData() {
        return await this.readFile(this.files.inventory);
    }

    /**
     * إضافة منتج جديد
     */
    async addProduct(productData) {
        const data = await this.getInventoryData();
        const newProduct = {
            id: data.nextProductId,
            ...productData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        data.products.push(newProduct);
        data.nextProductId++;
        
        await this.writeFile(this.files.inventory, data);
        return newProduct;
    }

    // ==================== سجلات الدخول - Login Logs ====================

    /**
     * الحصول على سجلات الدخول
     */
    async getLoginLogs() {
        return await this.readFile(this.files.loginLogs);
    }

    /**
     * إضافة سجل دخول جديد
     */
    async addLoginLog(logData) {
        const data = await this.getLoginLogs();
        const newLog = {
            id: data.nextLogId,
            ...logData,
            loginTime: new Date().toISOString()
        };
        
        data.loginLogs.push(newLog);
        data.nextLogId++;
        
        await this.writeFile(this.files.loginLogs, data);
        return newLog;
    }

    // ==================== الفواتير - Invoices ====================

    /**
     * الحصول على بيانات الفواتير
     */
    async getInvoicesData() {
        return await this.readFile(this.files.invoices);
    }

    /**
     * إضافة فاتورة جديدة
     */
    async addInvoice(invoiceData) {
        const data = await this.getInvoicesData();
        const newInvoice = {
            id: data.nextInvoiceId,
            invoiceNumber: `INV-${new Date().getFullYear()}-${String(data.invoiceCounter).padStart(3, '0')}`,
            ...invoiceData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        data.invoices.push(newInvoice);
        data.nextInvoiceId++;
        data.invoiceCounter++;
        
        await this.writeFile(this.files.invoices, data);
        return newInvoice;
    }

    // ==================== التحديثات - Updates ====================

    /**
     * الحصول على بيانات التحديثات
     */
    async getUpdatesData() {
        return await this.readFile(this.files.updates);
    }

    /**
     * إضافة تحديث جديد
     */
    async addUpdate(updateData, updateType = 'data') {
        const data = await this.getUpdatesData();
        let newUpdate;
        
        switch (updateType) {
            case 'system':
                newUpdate = {
                    id: data.nextSystemUpdateId,
                    ...updateData,
                    releaseDate: new Date().toISOString()
                };
                data.systemUpdates.push(newUpdate);
                data.nextSystemUpdateId++;
                break;
                
            case 'price':
                newUpdate = {
                    id: data.nextPriceUpdateId,
                    ...updateData,
                    updateTime: new Date().toISOString()
                };
                data.priceUpdates.push(newUpdate);
                data.nextPriceUpdateId++;
                break;
                
            case 'inventory':
                newUpdate = {
                    id: data.nextInventoryUpdateId,
                    ...updateData,
                    updateTime: new Date().toISOString()
                };
                data.inventoryUpdates.push(newUpdate);
                data.nextInventoryUpdateId++;
                break;
                
            default: // data updates
                newUpdate = {
                    id: data.nextDataUpdateId,
                    ...updateData,
                    updateTime: new Date().toISOString()
                };
                data.dataUpdates.push(newUpdate);
                data.nextDataUpdateId++;
        }
        
        await this.writeFile(this.files.updates, data);
        return newUpdate;
    }
}

module.exports = new DatabaseService();
